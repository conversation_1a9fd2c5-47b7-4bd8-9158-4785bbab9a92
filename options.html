<!DOCTYPE html>
<html lang="en" ng-app="newTab">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>New Tab Redirect</title>
    <script type="text/javascript" src="js/vendor/angular/angular-loader.min.js"></script>
    <link rel="stylesheet" href="css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="css/common.css" type="text/css">
    <link rel="stylesheet" href="css/options.css" type="text/css">
</head>
<body class="options-page">
<div ng-cloak>
    <div class="main-container" ng-controller="OptionsController">

        <div class="app-header">
            <span class="app-logo"><a href="options.html" target="_self"><i class="fa">&nbsp;</i> New Tab Redirect Options</a></span>
            <span class="webstore"><a href="https://chrome.google.com/webstore" target="_self" class="plain"><i class="fa" style="background:url('/_favicon/?pageUrl=chrome.google.com/webstore/category/apps') no-repeat;"></i> <x data-role="store">Chrome Web Store</x></a></span>
        </div>

        <div class="container options-container">
            <ul class="options-tabs">
                <li ng-class="{'selected': selected === 'url'}"><a ng-click="selected='url'">URL</a></li>
                <li ng-class="{'selected': selected === 'permissions'}"><a ng-click="selected='permissions'">Permissions</a></li>
                <li ng-class="{'selected': selected === 'contact'}"><a ng-click="selected='contact'">Contact</a></li>
                <li ng-class="{'selected': selected === 'donate'}"><a ng-click="selected='donate'">Donate</a></li>
            </ul>
            <div class="target">
                <div ng-show="selected=='url'">
                    <h2>Redirect URL</h2>
                    <input name="url" type="search" ng-model="url" placeholder="Default: New Tab Redirect 'Apps' Page" style="min-width:250px;" />
                    <button style="font-weight:bold" title="Save" ng-click="save()">Save</button>
                    <button title="Cancel" ng-click="cancel()">Cancel</button>
                    <button title="One-time get Synced URL" ng-click="getSyncedUrl()" class="fa fa-cloud-download"></button>
                    <p>
                        <label>
                            <input type="checkbox" ng-model="alwaysTabUpdate" ng-change="changeRedirect(alwaysTabUpdate)"> Always update tab, not redirect. <small>(Enable for cursor in the address bar)</small>
                        </label>
                    </p>
                    <p>
                        <label>
                            <input type="checkbox" ng-model="sync" ng-change="changeSync(sync)"> Sync this URL across browsers?
                        </label>
                    </p>
                    <h3>One click save...</h3>
                    <div class="column2">
                        <h4>Popular Pages</h4>
                        <div ng-repeat="page in popular">
                            <a href="{{page.location}}" class="bookmark" style="font-size:1.1em" ng-click="quickSave(page.location, $event)">
                                <i class="fa" style="background:url('_favicon/?pageUrl={{page.location}}') no-repeat"></i>
                                <span ng-bind="page.name"></span>
                            </a>
                        </div>
                        <h4>Chrome Pages</h4>
                        <div ng-repeat="page in internal">
                            <a href="{{page.location}}" class="bookmark" style="font-size:1.1em" ng-click="quickSave(page.location, $event)">
                                <i class="fa" style="background:url('_favicon/?pageUrl={{page.location}}') no-repeat"></i>
                                <span ng-bind="page.name"></span>
                            </a>
                        </div>
                    </div>
                </div>
                <div ng-show="selected=='permissions'">
                    <h2>Permissions</h2>
                    <p>
                        For information regarding what these permissions mean, please research:
                    </p>
                    <ul>
                        <li>
                            <a href="http://developer.chrome.com/extensions/permission_warnings" target="_blank">Chrome extensions: permission warnings</a>
                        </li>
                        <li>
                            <a href="https://support.google.com/chrome_webstore/answer/186213?hl=en" target="_blank">
                                Chrome Web Store Help: Permissions requested by apps and extensions
                            </a>
                        </li>
                    </ul>
                    <p>
                        If you have any doubts or questions, email <NAME_EMAIL>.
                    </p>
                    <p>
                        Never enable permissions you don't trust or don't understand, it can be dangerous.
                    </p>
                    <h3>Required Permissions</h3>
                    <ul>
                        <li ng-repeat="req in required_permissions"><span ng-bind="req"></span></li>
                    </ul>
                    <h3>Optional Permissions</h3>
                    <ul>
                        <li ng-repeat="perm in optional_permissions">
                            <span ng-bind="perm" style="width:90px;display:inline-block;"></span> <button ng-click="grant(perm)" ng-disabled="permissions[perm]">Approve</button> <button ng-click="deny(perm)" ng-disabled="!permissions[perm]">Deny</button>
                        </li>
                    </ul>
                    <h3>Where these are used</h3>
                    <p>
                        The <em>required permissions</em> are used mostly by the options page.
                    </p>
                    <p>
                        They allow the extension to display icons and save data either locally or to your Google Account (if you've enabled syncing in your browser and enabled extension syncing).
                    </p>
                    <p>
                        The <em>optional permissions</em> are used by the custom new tab 'Apps' page.
                    </p>
                    <p>
                        All of the optional permissions provide more functionality than the 'Apps' page needs, but extensions have to select permissions in their entirety for even a small subset of abilities. Here's how the 'Apps' page uses them.
                    </p>
                    <dl>
                        <dt>
                            <em>tabs</em>: <strong>"Access your tabs and browsing activity"</strong>
                        </dt>
                        <dd>
                            The 'tabs' permission is used to open apps in a new window (available in the rollover links). It's a nice-to-have feature, but is not necessary. If you don't trust this permission, leave it disabled. Only power users would be opening apps in new windows. See the <a href="http://developer.chrome.com/extensions/tabs" title="tabs summary" target="_blank">development documents</a> for more.
                        </dd>
                         <dt>
                             <em>topSites</em>: <strong>"Read and modify your browsing history"</strong>
                         </dt>
                         <dd>
                             This permission is bundled in the same warning as the history permission (which isn't used by this extension). The 'topSites' permission simply allows getting the top 20 most visited sites. The warning you get when enabling this permission is silly because it can't read any more of your history than the recent sites, and has no way to modify your history. See the <a href="http://developer.chrome.com/extensions/topSites" title="topSites summary" target="_blank">development documents</a> for more.
                         </dd>
                         <dt>
                             <em>management</em>: <strong>"Manage your apps, extensions, and themes"</strong>
                         </dt>
                        <dd>
                            The custom 'Apps' page only needs the 'apps' part of this permission. In fact, I have to filter out extensions and themes because everything is returned in the query. By 'manage', this means the extension can allow you to view, create, edit, or delete apps. Currently, the custom 'Apps' page retrieves apps and allows you to uninstall apps directly from within the 'Apps' page. You're provided confirmation. Generally, you should be cautious about enabling this permission in extensions, because it also allows extensions to install apps or uninstall the extension itself. Unfortunately, the permission is all or nothing, otherwise I'd request the bare minimum. This is the only required permission for the custom 'Apps' page to make sense.  See the <a href="http://developer.chrome.com/extensions/management" title="management summary" target="_blank">development documents</a> for more.
                        </dd>
                        <dt>
                            <em>bookmarks</em>: <strong>"Read and modify your bookmarks"</strong>
                        </dt>
                        <dd>
                            Again, an all-or-nothing permission. The 'Apps' page can display between 5 and 40 bookmarks from your bookmarks bar. There's no modification going on. See the <a href="http://developer.chrome.com/extensions/bookmarks" title="bookmarks summary" target="_blank">development documents</a> for more.
                        </dd>
                    </dl>
                </div>
                <div ng-show="selected=='donate'" >
                    <h2>Donate!</h2>
                    <p>
                        Some users insisted in 2011 or 2012 that I accept donations. I'm a full-time software engineer and I created this extension as a fun project, so I was reluctant to comply. I'd be just as happy with a nice email or a follow on Twitter. But, if you like the extension <em>that much</em> you can donate $1 or so to buy me a coffee. That's what usually fuels my development.
                    </p>
                    <ng-include src="'js/partials/donate.html'" />
                </div>
                <div ng-show="selected=='contact'">

                    <h2><span class="github-icon">Bugs or Features</span></h2>

                    <p>
                        Please post any new bugs or feature requests to <a href="http://bit.ly/1mU2Ww3" target="_blank">github</a>.
                    </p>

                    <p>There's a chance that your question is already answered in the <a href="http://bit.ly/Qw6rus"
                                                                                         target="_blank">FAQ</a>, so
                        check there before reporting an issue.
                    </p>

                    <p><strong>NOTE</strong> I have no control over how Chrome highlights or focuses the address bar with this extension.</p>

                    <p>
                        Also, feel free to fork the code and contribute.
                    </p>

                    <h2><span class="google-icon">Email</span></h2>

                    <p>You're welcome to <a href="mailto:<EMAIL>">email me</a> or chat with me on Google (<EMAIL>).
                    </p>

                </div>
            </div>
            <span class="clear"></span>
            <div class="saved" ng-class="{'show-saved': show_saved }">
                <div class="saved-container">Options saved!</div>
            </div>
        </div>
    </div>
</div>

<footer>
    <div style="float:left;">
        <p>
            <a href="http://www.ipreferjim.com">&copy; James Schubert, 2009-2023</a>
            &nbsp;|&nbsp;
            <a href="http://github.com/jimschubert/newtab-redirect" target="_blank">Report bugs</a>
            &nbsp;|&nbsp;
            <a href="https://groups.google.com/forum/#!forum/new-tab-redirect-beta-testers" target="_blank">Beta test</a>
            &nbsp;|&nbsp;
            <a href="welcome.html" target="_blank">'Welcome'</a>
        </p>
    </div>
</footer>

<script type="text/javascript" src="js/vendor/angular/angular.min.js"></script>

<script type="text/javascript" src="js/controllers.js"></script>
<script type="text/javascript" src="js/options_constants.js"></script>
<script type="text/javascript" src="js/options_controller.js"></script>
<script type="text/javascript" src="js/directives.js"></script>
<script type="text/javascript" src="js/filters.js"></script>
<script type="text/javascript" src="js/services.js"></script>
<script type="text/javascript" src="js/app.js"></script>
</body>
</html>
