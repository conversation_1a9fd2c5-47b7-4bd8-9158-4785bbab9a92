<!DOCTYPE html>
<html>
<head>
    <title>Welcome!</title>
    <link rel="stylesheet" href="css/font-awesome.min.css" type="text/css" />
    <link rel="stylesheet" href="css/common.css" type="text/css" />
    <link rel="stylesheet" href="css/welcome.css" type="text/css" />
    <script src="js/welcome.js"></script>
</head>
<body>
<div style="display:none;">Hello, world. ;)</div>
<div id="container">
    <div id="left_indicator" class="nav-btn nav prev front in ghost"></div>
    <div id="right_indicator" class="nav-btn nav next front in"></div>
    <button id="btn_prev" class="nav-btn nav prev back in"></button>
    <button id="btn_next" class="nav-btn nav next back in"></button>

    <div class="slick">
      <div id="welcome_page" class="slider">
        <h1><span class="welcome">Welcome!</span></h1>
        <p class="blah">
            You've just installed New Tab Redirect. Thanks!
        </p>
        <p class="blah">
            <a href="options.html" class="pretty-links">Set Options</a>
            <a href="http://bit.ly/1mU2RIM" target="_blank" class="pretty-links">View History</a>
            <a href="changes.txt" target="_blank" class="pretty-links">View changes.txt</a>
        </p>
        <p class="blah">
            New Tab Redirect is an extension for Google Chrome
            which will allow you to open <em>any</em> URL when
            creating a new tab. That's pretty cool for pages on
            the internet. But... you can also specify a local file
            like a custom *.html file or a text file!
        </p>
        <p class="blah">
            <i class="fa fa-tags" style="color:rgb(69, 253, 6);"></i> New Tab Redirect now provides a default new tab experience.
            Many users rely on <code>chrome://apps/</code>, but also wanted
            the address bar to be clear and focused for quick search. Others
            disliked some features of Chrome's original New Tab page like the
            'Most Visited Sites' or 'Bookmarks Bar'. New Tab Redirect now offers
            these features with an option to hide or show them. You can choose to
            show between 5-20 top sites and between 5-40 bookmarks. If this is the
            behavior you want, you don't even have to visit the options page. Just
            create a new tab and check it out!
        </p>
        <p class="blah">
            Click the arrows on the sides of this page (or press the right arrow key) for an introduction to the extension.
        </p>
        <p class="blah">
            If you want to skip the intro, that's cool. You can press CTRL+T or
            click the new tab button at the top of the browser. Or, you can click
            <a href="options.html">here</a> to access the options page.
        </p>
      </div><!-- end Intro -->

      <div id="intro_page" class="slider" style="-webkit-transform: translate3d(2000, 0, 0);position:absolute;top:0;display:none;">
        <h1><span class="welcome">Introduction</span></h1>
        <div id="intro_screenshot" class="default">
            
            <ul id="normal_ss" class="screenshots_list">
                <li data-role="screenshot" data-apply="ss-url">Enter a URL</li>
                <li data-role="screenshot" data-apply="ss-sync">Check 'Sync Options'</li>
                <li data-role="screenshot" data-apply="ss-save">Click Save</li>
                <li data-role="screenshot" data-apply="ss-saved">Notice 'Options Saved'</li>
            </ul>
            
            <ul id="quicksave_ss" class="screenshots_list">
                <li data-role="screenshot" data-apply="ss-quick">Click on any quick save button</li>
                <li data-role="screenshot" data-apply="ss-saved">Notice 'Options Saved'</li>
            </ul>
            
            <span class="intro_explain">
                Place your mouse pointer over any item above to update the preview to the left.
            </span>
            <span class="intro_explain">
                'Sync Options' allows you to sync your URL and welcome page preferences across browsers
                using <a href="http://bit.ly/1mU2LB0" target="_blank">Google's Chrome Sync</a>.
                You must be <a href="http://bit.ly/1mU2Nsw" target="_blank">signed in</a> to the browser.
            </span>
        </div>
      </div>
      
      <div id="contact_page" class="slider" style="-webkit-transform: translate3d(2000, 0, 0);position:absolute;top:0;display:none;">
        <h1><span class="welcome">Contact</span></h1>

        <h2><span class="github-icon">Bugs or Features</span></h2>
        Please post any issues to <a href="http://bit.ly/1mU2Ww3" target="_blank">github.</a>
        Also, feel free to fork the code and contribute.

        <h2><span class="twitter-icon">Twitter</span></h2>
        I tweet sometimes from @jimschubert. I'm not as active as most people on twitter.
        <p>
          <a href="http://bit.ly/1mU2ZI7">Follow me!</a>
        </p>

        <h2><span class="google-icon">Email</span></h2>
        You're welcome to email me or chat with me on Google.
        <p class="blah"><a href="mailto:<EMAIL>"><EMAIL></a></p>

        <p class="blah">
        I don't expect donations, but if you'd like to contribute I won't argue.
        </p>
        <a href="https://www.patreon.com/bePatron?u=7545347" class="patreon-link">
            <span class="patreon-link-left">
            <svg viewBox="0 0 569 546" xmlns="http://www.w3.org/2000/svg"><g><circle data-fill="1" id="Oval" cx="362.589996" cy="204.589996" r="204.589996"></circle><rect data-fill="2" id="Rectangle" x="0" y="0" width="100" height="545.799988"></rect></g></svg>
            </span>
                  <span class="patreon-link-right">
            Become a Patron!
            </span>
        </a>

      </div><!-- end Contact -->

      <div id="faq_page" class="slider" style="-webkit-transform: translate3d(2000, 0, 0);position:absolute;top:0;display:none;">
        <h1><span class="welcome">FAQ</span></h1>

        <blockquote>How do I access this extension?</blockquote>
        <p class="blah answer">
          In the top-right of your Google Chrome browser, click the settings icon: <a href="chrome://settings" class="settings-link">&nbsp;</a>,
          then select <code>More Tools -> Extensions</code>. A new tab will appear.
          Alternatively, you can type
          <a href="chrome://settings/extensions">chrome://extensions/</a> into the address bar.
        </p>
        <p class="blah answer">Scroll down to New Tab Redirect! and click Options.</p>

        <blockquote>How do I uninstall this extension?</blockquote>
        <p class="blah answer">Follow the directions above, but choose Uninstall.</p>

        <blockquote>I'd like to buy your extension, is it for sale?</blockquote>
        <p class="blah answer">
          No. It is not for sale for any amount. Thanks, though.
        </p>

        <p>
          Many other questions are answered on <a href="http://bit.ly/1kXFclX">the wiki</a>.
        </p>

        <h2>That's it!</h2>
        <a href="options.html" target="_blank" class="pretty-links">Set your Options!</a>

      </div><!-- end FAQ -->
    </div>

<div id="footer">

  <ul id="bottom_nav">
    <li class="welcome-icon welcome_page list-nav selected" style="height:36px">&nbsp;</li>
    <li class="intro_page list-nav"><span>Intro</span></li>
    <li class="contact_page list-nav"><span>Contact</span></li>
    <li class="faq_page list-nav"><span>FAQ</span></li>
  </ul>

</div>

</div>
</body>
</html>
