v3.1.6
 * Migrate to Manifest v3
 * Contribution from <PERSON><PERSON>, fixing file:// loading
v3.1.5
 * Fix some links (pledgie, Google+)
 * Change donation link to Patreon
 * Add clarification note in "Contact" page that I have no control over address bar highlight/focus.
v3.1.4
 * Fix bug (apps page) where scripts sometimes load out of order causing redirect to fail
 * Fix rendering issue with font sizes. Recent chrome update changed the display of 0.6em.
v3.1.2
 * Fix bug (apps page) where bookmarks to local file did not load
 * Fix bug (apps page) where chrome now requires user gesture to delete app, so I can't call
   chrome.management.uninstall from within a promise or deeply nested in jqlite click
 * Fix bug (apps page) where bookmarks would only load for browsers set to English locales
 * Added logging to all promise rejections to better debug user scenarios (like uninstall)
 * Fix confusing usage of href attributes for binding URLs to directives in apps page
 * Merge 'selected'->'highlighted' contribution
v3.1.1
 * Fix bug introduced in 3.1 where cursor is in address bar rather than page.
v3.1
 * Made the 'Apps' page extra permissions optional and individually configurable
 * Added icon fix for webpages that are saved as apps at chrome://apps (this extension
   doesn't currently support the same feature)
 * Reworked options page to match new 'Apps' styles
 * Added ability to grant or deny permissions on the options page
 * Moved donation to a sub-nav on the options page
 * Added explicit MIT license file after finding derivative works online with no attribution
 * Added full contact information directly to the options page (was previously welcome page only)
 * Fix Chrome 34 'bug' which breaks querying for "Bookmarks Bar"
v3.0
 * Added a no-redirect option for the new tab page, which provides an
   experience similar to Chrome's original page (including search bar focus)
   NOTE: clear, searchable address bar only works on the New Tab Redirect Apps page
 * Added permissions:
    - "topSites": required to display top 10 visited sites
    - "management": required to allow user to display, launch, and
       uninstall Chrome apps within built-in new tab page
    - "bookmarks": required to display first 10 bookmarks bar links
    - "chrome://favicon/": required to display bookmark icons
v2.2
 * Remove chrome-internal://newtab/ support
 * Add explicit https:// on sites that support https under quick links
 * Updated fontawesome to 4.0.3
 * Added two new tab override examples: empty page, redirect page
v2.1
 * Fix antiquated mentioning of "Wrench" icon
 * Add chrome://apps as an option
 * Sync improvements for multiple-machine installations
 * Don't display Welcome Page when Chrome updates
v2.0
 * Added welcome page to display on install/upgrade
 * Added option to suppress welcome page on upgrades
 * Changed background page to event page to use less memory
 * Changed from localStorage to chrome.storage.* api,
   default to 'local' namespace
 * Added option to sync options via Chrome Sync
    NOTE: sync will *ONLY* happen after selecting 'Sync' and clicking save.
 * Added 'Apps' button to options page so users can access other installed
   applications and extensions using the original New Tab page
 * Moved most of processing from background.js to options.js and 
   redirect.js to eliminate the need to request background page
 * Fixed incognito mode (side effect of chrome.storage.*)
