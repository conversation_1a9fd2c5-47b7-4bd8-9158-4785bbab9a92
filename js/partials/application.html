<div class="app-icon">
    <a ng-href="{{app.appLaunchUrl}}" chrome-href="app.appLaunchUrl" chrome-launch="app.id" chrome-type="app.type" class="app-icon-128"
        ng-class="{webpage: app.icons[0].size == 16 && app.icons[1] == undefined }">
        <img ng-src="{{app.icons|iconsize:128:app}}" title="{{app.name}}" />
        <span class="app-desc">{{app.name}}</span>
    </a>

    <div class="app-actions">
        <a chrome-href="app.appLaunchUrl" ng-if="app.type != 'packaged_app'" chrome-pinned="app.id"
           title="Open {{app.name}} in a pinned tab" class="special-href"><i class="fa fa-2x fa-thumb-tack"></i></a>
        <a chrome-href="app.appLaunchUrl" ng-if="app.type != 'packaged_app'" chrome-new-tab="app.id"
           title="Open {{app.name}} in a new tab" class="special-href"><i class="fa fa-2x fa-level-up"></i></a>
        <a chrome-href="app.appLaunchUrl" ng-if="app.type != 'packaged_app' && permissions.tabs == true" chrome-new-window="app.id"
           title="Open {{app.name}} in a new window" class="special-href"><i class="fa fa-2x fa-external-link"></i></a>
        <a chrome-href="app.optionsUrl" chrome-options="app.id" ng-if="app.optionsUrl" title="Open options for {{app.name}}" class="special-href"><i
                class="fa fa-2x fa-wrench"></i></a>
        <div chrome-uninstall="app.id" chrome-uninstall-name="app.name"></div>
    </div>
</div>