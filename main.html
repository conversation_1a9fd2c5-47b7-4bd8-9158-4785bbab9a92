<!doctype html>
<html lang="en" ng-app="newTab">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>New Tab Redirect</title>
    <script type="text/javascript" src="js/defer_bootstrap.js"></script>
    <link rel="stylesheet" href="css/font-awesome.min.css" type="text/css">
    <link rel="stylesheet" href="css/common.css" type="text/css">
    <script type="text/javascript" src="js/vendor/angular/angular-loader.min.js"></script>
    <script type="text/javascript" src="js/vendor/angular/angular.min.js"></script>
</head>
<body>

<div ng-cloak>
    <div class="main-container" ng-controller="MainController">

        <span id="app-prefs"><a href="#" class="plain" ng-click="show_prefs=true"><i class="fa fa-gears"></i></a></span>

        <div class="dialog" ng-show="show_prefs">
            <div class="content">
                <h2>Display Preferences</h2>
                <div class="setting">
                    <h3><i class="fa fa-bookmark-o"></i> Bookmarks Bar</h3>
                    <toggle-permission permission="bookmarks" granted="permissions.bookmarks"></toggle-permission>
                    <label>
                        <input type="checkbox" ng-model="enable_bookmarks" ng-disabled="!permissions.bookmarks"> Enable
                    </label>
                    <div>
                        Show <input type="number" min="5" max="40" ng-model="bookmark_count" ng-disabled="!permissions.bookmarks" title="Too many bookmarks will cause the box to become very large"> bookmarks
                    </div>
                </div>
                <div class="setting">
                    <h3><i class="fa fa-star-o"></i> Top Sites Panel</h3>
                    <toggle-permission permission="topSites" granted="permissions.topSites"></toggle-permission>
                    <label>
                        <input type="checkbox" ng-model="enable_top" ng-disabled="!permissions.topSites"> Enable
                    </label>
                    <div>
                        Show <input type="number" min="5" max="20" ng-model="top_count" ng-disabled="!permissions.topSites" title="Too many top sites will cause the box to become very large"> top sites
                    </div>
                </div>

                <div class="setting">
                    <h3><i class="fa fa-external-link"></i> Open apps in New Window</h3>
                    <toggle-permission permission="tabs" granted="permissions.tabs"></toggle-permission>
                </div>

                <button ng-click="save_preferences()" class="pull-right">Close</button>
            </div>
        </div>

        <div class="app-header">
            <span class="app-logo"><a href="options.html" target="_self"><i class="fa">&nbsp;</i> {{extension_name}}</a></span>
            <span class="webstore"><a href="https://chrome.google.com/webstore" target="_self" class="plain"><i class="fa" style="background:url('/_favicon/?pageUrl=https://chrome.google.com/webstore/category/apps') no-repeat;"></i> Chrome Web Store</a></span>
        </div>

        <div class="container bookmarks-container" ng-show="permissions.bookmarks && enable_bookmarks && bookmarks.length > 0" ng-class="{'populated':bookmarks.length > 0}">
            <x ng-if="enable_bookmarks" ng-repeat="b in bookmarks">
                <a class="bookmark" ng-href="{{b.url}}" title="{{b.title}}" target="_self" ng-if="is_special_uri(b.url) == false">
                    <i class="fa" style="background:url('_favicon/?pageUrl={{b.url||'example.com'}}') no-repeat"></i>
                    <span>{{b.title}}</span>
                </a>
                <a class="bookmark special-href" ng-click="navigate(b.url)" title="{{b.title}}" ng-if="is_special_uri(b.url) == true">
                    <i class="fa" style="background:url('_favicon/?pageUrl={{b.url||'example.com'}}') no-repeat"></i>
                    <span>{{b.title}}</span>
                </a>
            </x>
        </div>

        <div ng-show="permissions.management" class="container app-container clear" ng-class="{'after-bookmarks': enable_bookmarks && bookmarks.length > 0,'populated':apps.length > 0}">
            <div><input type="search" ng-model="q.name" ng-show="apps.length > 5" placeholder="Filter apps"></div>
            <chrome-app ng-repeat="app in (apps | filter:q)" app="app" permissions="permissions"></chrome-app>
            <div ng-show="(apps | filter:q).length == 0" style="margin:1.5em">No matches found.</div>
            <span class="clear"></span>
        </div>

        <div ng-if="!permissions.management" class="container app-container no-app-permissions"
             ng-class="{'after-bookmarks': enable_bookmarks && bookmarks.length > 0, 'populated': permissions.management == false}">
            <h3>Hello.</h3>
            <p>
                This extension doesn't yet have the permissions to read and manage your Apps!
                It requires the 'management' permission to do this. You can read more about <a href="https://support.google.com/chrome_webstore/answer/186213?hl=en" target="_blank">permissions</a> and
                their <a href="http://developer.chrome.com/extensions/permission_warnings" target="_blank">warnings</a> before proceeding.
            </p>
            <p>
                <toggle-permission permission="management" granted="permissions.management"></toggle-permission>
            </p>
            <p>
                Permissions and display options can be configured by clicking the gear icon in the bottom right corner.
                That's where you'll find extra, opt-in functionality like the top sites panel and quick bookmarks 'bar'.
            </p>
            <p>
                You'll only see that annoying popup to request permissions once. After that, you can grant or deny any individual
                permissions at any time without the extra popup (except the management permission, because what good's an Apps page without that?).
            </p>
            <p>
                Enable what you want, disable what you don't. You can always check the permissions of any installed extension or app at chrome://extensions (also accessible under <code>Menu -> Tools -> Extensions</code>. Safe browsing!
            </p>
            <span class="clear"></span>
        </div>

        <div id="top-sites" ng-if="permissions.topSites && enable_top && top.length > 0">
            <div class="top-sites-wrapper">
                <span class="top-sites-header">Top Sites</span>
                <div class="inner">
                    <a ng-repeat="site in top" href="{{site.url}}" title="{{site.title}}"
                       target="_self" class="bookmark">
                        <i class="fa" style="background:url('_favicon/?pageUrl={{site.url}}') no-repeat"></i>
                        <span>{{site.title}}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="js/controllers.js"></script>
<script type="text/javascript" src="js/directives.js"></script>
<script type="text/javascript" src="js/filters.js"></script>
<script type="text/javascript" src="js/services.js"></script>
<script type="text/javascript" src="js/app.js"></script>
<script type="text/javascript" src="js/redirect.js"></script>
</body>
</html>
