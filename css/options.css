footer {
    font-size:0.8em;
    bottom: 0;
    position: fixed;
    display: block;
    margin-top: 0;
    margin-left: auto;
    margin-right: auto;
    height: 40px;
    padding: 0 2px 5px 8px;
    max-width: 99%;
    width: 100%;
    background: #FFFFFF;
}

body.options-page {
    font-size: 0.9em;
    margin: 40px auto 80px;
    height:auto;
}

div.options-container {
    width:100%;
    height:100%;
}

div.options-container .target {
    display:inline-block;
    min-width: 400px;
    margin-left:12px;
    max-width: 550px;
}

ul.options-tabs {
    display: inline-block;
    vertical-align: top;
    list-style-type: none;
    padding: 0.2em 0.2em;
    margin: 0 15px 0 0;
    width: 140px;
}

ul.options-tabs li {
    display:block;
    height: 2em;
    width:115px;
    vertical-align: middle;
}

ul.options-tabs li:hover {
    background-color:  rgba(186,211,218,0.8);
    cursor: pointer;
}

ul.options-tabs li.selected {
    background-color:  rgba(186,211,218,0.8);
}

ul.options-tabs li a {
    color: black;
    cursor: auto;
    text-decoration: none;
    display:block;
    padding:0.4em;
    font-weight: 600;
}

ul.options-tabs li.selected a {
    font-weight: 800;
}

ul.options-tabs li:not(.selected) a:hover {
    cursor: pointer;
    font-weight: 800;
}

.column2 {
    -webkit-column-count: 2;
    -webkit-column-gap: 12px;
}

.column2 h4 {
    -webkit-column-span: all;
}

h3 { margin: 8px 0; }
h4 { margin: 4px 0; }

.saved {
    position: absolute;
    top: 40px;
    right: 15px;
    width: 180px;
    transition: all 0.3s ease-in-out;
    transition-delay:100ms;
    opacity:0;
    z-index:-999999;
}

.saved-container {
    padding: 14px;
    font-size: 1.3em;
    border: 2px solid #7c82bf;
    background-color: rgba(180, 255, 165, 0.8);
    border-radius: 7px;
    font-weight: 800;
}

.saved.show-saved{
    opacity:1;
    z-index:999999;
}

@media only screen and (max-width:750px) {

    /* Smaller resolution, show tabs across the top */
    ul.options-tabs {
        display: inline;
    }

    ul.options-tabs li {
        display: inline-block;
        text-align: center;
    }

    /* Position "Options Saved" a little lower */
    .saved { top: 80px; }
}

@media only screen and (max-width:545px) {

    /* Hide superfluous stuff when really small resolution */
    a[rel="publisher"],
    x[data-role="store"] {
       display:none !important;
    }

}