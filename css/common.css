@charset "UTF-8";

[ng\:cloak], [ng-cloak], [data-ng-cloak], [x-ng-cloak],
.ng-cloak, .x-ng-cloak,
.ng-hide {
    display: none !important;
}

ng\:form {
    display: block;
}

::-webkit-scrollbar {
  width:8px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 2px rgba(155,155,155,0.9);
  -webkit-border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 4px rgba(0,0,0,0.6);
  -webkit-border-radius:5px;
  background: rgba(155,155,155,0.8);
}

p {
  -webkit-margin-after: 0.5em;
}

html,body {
padding:0;
margin:0;
min-height:200px;
max-height:4000px;
}

blockquote {
  padding: 0 0 0 1em;
  border-left: 5px solid grey;
  margin: 4px 0;
}

body {
  min-width:560px;
  color: rgba(10,10,10,0.7);
  font-family: sans-serif;
  background-color: rgba(250,250,250,1);
  overflow-x:auto;
}

h2 {
  font-size:1.2em;
  margin-bottom:3px;
}

a {
  color: rgba(0,0,255,0.7);
  text-decoration: none;
}

a:hover {
  color: rgba(0,0,255,0.8);
  text-decoration: underline;
}

a.plain {
color: rgba(10,10,10,0.7);
text-decoration: none;
}

a.plain:hover { text-decoration: underline; }

a[rel=publisher] {
  padding-top:3px;
  float:right;
  text-decoration:none;
  display:inline-block;
  color:#333;
  text-align: center;
  font:13px/16px arial,sans-serif;
  white-space:nowrap;
}

a[rel=publisher] span {
  display:inline-block;
  vertical-align:top;
  margin-right:5px;
  margin-top:8px;
}

a[rel=publisher] span.name {
  font-weight: bold;
}

a[rel=publisher] img {
  border:0;
  width:32px;
  height:32px;
}

.in {
  display:inline;
}

.front { z-index: 9999; }
.back { z-index: 1000; }

.pretty-links {
  margin: 0.2em 0.4em;
}

.ghost { opacity: 0.1 !important; }

.github-icon {
  padding-left:33px;
  background: url(../images/github_32.png) no-repeat left center;
}

.twitter-icon {
  padding-left:33px;
  background: url(../images/twitter_32.png) no-repeat left center;
}

.google-icon {
  padding-left:33px;
  background: url(../images/google_32.png) no-repeat left center;
}

.app-icon-128 {
    display:block;
    height:150px;
    width:150px;
    padding:7px;
}

.app-icon-128 img {
    height:128px;
    width:128px;
}

.app-icon-128.webpage {
    background: url(../images/document-new.svg) no-repeat top;
    background-size: 135px auto;
}

.app-icon-128.webpage img {
    height: 16px;
    width: 16px;
    margin-top: 112px;
}

div.app-icon {
    float: left;
    text-align: center;
    line-height: 1.2em;
    font-size: 0.9em;
}

.app-logo a,
div.app-icon a {
    color: rgba(0,0,0,0.7);
    text-decoration: none;
}

div.app-actions {
    display:none;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    background-color:rgba(186,211,218,0.8);
    position:absolute;
    bottom:0;
    margin-left:5px;
    margin-right:5px;
    padding-top:4px;
    padding-bottom:3px;
    width:100%;
}

div.app-actions a {
    padding:3px;
}

div[app] { position:relative; }
div[app]:hover div.app-actions {
    display:inherit;
}

span.app-desc {
    display: block;
    white-space: pre-wrap;
    text-decoration: none;
}

div.main-container {
}

div.container {
    margin-left: auto;
    margin-right: auto;
    width: 95%;
}

div.app-container {
    margin-top:35px;
    padding-bottom:40px;
    opacity:0;
    max-height:0;
}

/*undo the app-container stuff*/
div.no-app-permissions {
    opacity:1;
    padding:20px;
    max-height: 1000px;
    width:90%;
    display:none;
}

div.app-container.populated {
    opacity: 1;
    max-height:3000px;
    transition: all 300ms ease-in-out;
    transition-delay: 100ms;
    display:block;
}

div.app-container.after-bookmarks {
    margin-top: 10px;
}

div.app-container input[type='search'] {
    margin-left:25px;
}

div.bookmarks-container {
    margin-top:35px;
    padding:8px;
    width:90%;
    border-radius:4px;
    background-color: rgba(178, 255, 131, 0.90);
    opacity:0;
}

div.bookmarks-container.populated {
    opacity: 1;
    transition: all 320ms ease-in-out;
}

div.bookmarks-container .bookmark {
    display: inline-block;
    border-radius: 2px;
    background-color: rgba(255,255,255,0.4);
    padding: 4px 3px 2px 3px;
    -webkit-box-shadow: 1px 1px 2px 0 rgba(50, 50, 50, 0.66);
}

div.app-header {
    position: fixed;
    top: 0;
    height: 30px;
    background-color: rgba(186,211,218,0.8);
    width:100%;
    z-index: 10000;
}

div.app-header span {
    display: inline-block;
    font-size: 19px;
    font-weight: 700;
    line-height: 1em;
    padding-top:5px;
}

span.app-logo {
    margin-left:20px;
}

span.app-logo i {
    display:inline-block;
    height:19px;
    width:19px;
    background: url(../images/icon19.png) no-repeat center;
}

span.webstore {
    display:inline-block;
    float:right;
    margin-right:10px;
}
span.webstore a i {
    display:inline-block;
    height:16px;
    width:16px;
}

#top-sites {
    position: fixed;
    background: rgba(186,211,218,0.95);
    -webkit-transition-duration: 0.3s;
    border-radius: 0 5px 5px 0;
    width:15.4em;
    left:-14em;
    top:40px;
}

#top-sites .top-sites-wrapper {
    position:relative;
    height:auto;
    min-height:120px;
    padding-left: 2px;
    padding-right: 1.4em;
}

.top-sites-header {
    position: absolute;
    top: 3px;
    right: 1em;
    display: block;
    -webkit-transform: rotate(-90deg);
    -webkit-transform-origin: 100% 0%;
    font-size: 0.9em;
    margin:0.2em;
}

#top-sites:hover {
    left:0;
}

#top-sites .no-top {
    margin:15px;
    display:inline-block;
}

#top-sites .inner div {
    margin:3px;
    padding-top:1px;
}

a.bookmark {
    margin:3px;
    font-size:0.8em;
    display:block;
    max-width:190px;
    height:20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

a.special-href {
    cursor:pointer;
}

a.bookmark i {
    height: 16px; width: 16px;
}
a.bookmark i,
a.bookmark a {
    vertical-align: middle;
    height: 16px;
    padding-top:2px;
    padding-bottom:2px;
}

.main-container span.clear {
    display:block;
    clear:both;
    height:0;
}

span#app-prefs {
    position:fixed;
    bottom:10px;
    right:10px;
    background-color: rgba(255, 255, 255, 0.9);
    padding:0;
    border-radius: 2px;
    margin:3px;
    -webkit-box-shadow: 3px 3px 5px 0 rgba(50, 50, 50, 0.66);
    z-index: 99999;
}

span#app-prefs i {
    padding: 3px;
}

[ng-cloak] {
    display:none;
}

/*https://developer.mozilla.org/en-US/docs/Web/CSS/pointer-events*/
.dialog {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0,0,0,0.6);
    pointer-events: auto;
    -webkit-transition: all 500ms ease-out;
    -webtkit-transition-property: right, opacity;
    z-index: 9000000;
    opacity:1;
}
.dialog.ng-hide {
    opacity: 0;
    right: -800px;
    pointer-events: none;
}

.dialog .content {
    position: relative;
    width:200px;
    height:auto;
    margin-top:15px;
    margin-bottom:20px;
    margin-left:auto;
    margin-right:auto;
    padding:15px 20px 30px 20px;
    border-radius: 7px;
    background: rgba(255,255,255,0.95);
    -webkit-box-shadow: 3px 3px 5px 0 rgba(50, 50, 50, 0.66);
}

.dialog .content h2 { margin: 2px; }
.dialog .content h3 {
    font-size:0.8em;
    margin-bottom:5px;
}
.dialog .content div.setting { padding-bottom:8px; }

.patreon-link {
    -moz-box-align: center;
    align-items: center;
    backface-visibility: hidden;
    background-color: rgb(232, 91, 70);
    border-radius: 9999px;
    border: medium none;
    box-sizing: border-box;
    color: rgb(255, 255, 255) !important;
    cursor: pointer;
    display: inline-flex;
    font-size: 0.875rem !important;
    font-weight: 500;
    height: unset;
    -moz-box-pack: center;
    justify-content: center;
    padding: 0.594rem 1rem;
    position: relative;
    pointer-events: unset;
    text-align: center;
    text-decoration: none;
    text-transform: none;
    transition: all 300ms cubic-bezier(0.19, 1, 0.22, 1) 0s;
    -moz-user-select: none;
    white-space: unset;
    width: 100%;
}
.patreon-link-right {
    color: white;
}
.patreon-link-left svg {
    height: 1rem;
    width: 1rem;
    align-self: center;
    stroke-width: 1.2px;
    margin-right: 0.5rem;
}
.patreon-link-left svg circle {
    fill: white;
}

@media only screen and (max-width : 685px ) {
    div.bookmarks-container {margin-left:35px !important;}
}